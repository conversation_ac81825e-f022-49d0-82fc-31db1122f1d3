package org.dromara.aset.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.aset.domain.PayCoinTxnDtl;
import org.dromara.aset.domain.bo.PayCoinTxnDtlBo;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/25
 */

public interface IPayCoinTxnDtlService {

    boolean processRefundFromFrozenFunds(PayCoinTxnDtlBo bo);

    PayCoinTxnDtlVo queryById(Long id);

    BigDecimal sumWithdrawalAmount(PayCoinTxnDtlBo bo);

    TableDataInfo<PayCoinTxnDtlVo> queryWithdrawals(PayCoinTxnDtlBo bo, PageQuery pageQuery);

    TableDataInfo<PayCoinTxnDtlVo> queryWithdrawalsOld(PayCoinTxnDtlBo bo, PageQuery pageQuery);

    /**
     * 根据条件分页查询币种交易记录
     * @param bo
     * @param pageQuery
     * @return
     */
    IPage<PayCoinTxnDtlVo> queryPageByParams(PayCoinTxnDtlBo bo, PageQuery pageQuery);

    /**
     * 根据条件查询币种交易记录
     * @param bo
     * @return
     */
    List<PayCoinTxnDtlVo> queryListByParams(PayCoinTxnDtlBo bo);

    /**
     * 保存币种交易记录
     * @param payCoinTxnDtl
     * @return
     */
    boolean saveCoinTxnDtl(PayCoinTxnDtl payCoinTxnDtl);
}
